<template>
    <div class="page">
        <van-nav-bar title="房态图" left-arrow @click-left="$router.back()" class="nav-bar" />

        <!-- 统计信息 -->
        <div class="stats-bar" v-if="statsData">
            <div class="stats-item">
                <span class="stats-count">{{ statsData.totalCount }}</span>
                <span class="stats-label">总数</span>
            </div>
            <div class="stats-item">
                <span class="stats-count">{{ statsData.emptyCount }}</span>
                <span class="stats-label">空置</span>
            </div>
            <div class="stats-item">
                <span class="stats-count">{{ statsData.rentCount }}</span>
                <span class="stats-label">在租</span>
            </div>
            <div class="stats-item">
                <span class="stats-count">{{ statsData.toEffectCount }}</span>
                <span class="stats-label">待生效</span>
            </div>
            <div class="stats-item">
                <span class="stats-count">{{ statsData.invalidCount }}</span>
                <span class="stats-label">不可招商</span>
            </div>
        </div>

        <div class="filter-bar">
            <van-dropdown-menu class="dropdown-menu">
                <van-dropdown-item v-model="selectedParcel" :options="parcelOptions" @change="onParcelChange" />
                <van-dropdown-item v-model="selectedBuilding" :options="buildingOptions" @change="onBuildingChange" />
                <van-dropdown-item v-model="selectedFloor" :options="floorOptions" @change="onFloorChange" />
            </van-dropdown-menu>
            <van-button type="primary" size="small" class="more-btn" @click="showMoreFilter">更多</van-button>
        </div>

        <div class="legend">
            <div v-for="(item, index) in legendItems" :key="index" class="legend-item">
                <span :class="['legend-color', item.class, item.isHalf ? 'half' : '']" v-if="!item.isHalf"></span>
                <span :class="['legend-color-half']" v-else>
                    <span :class="['half', item.class]"></span>
                </span>
                <span class="legend-text">{{ item.text }}</span>
            </div>
        </div>

        <!-- 加载状态 -->
        <van-loading v-if="loading" class="loading-center" size="24px" vertical>
            加载房态数据中...
        </van-loading>

        <!-- 空状态 -->
        <van-empty v-else-if="!loading && (!diagramData || diagramData.floorDiagramList.length === 0)"
            description="暂无房态数据" />

        <!-- 房态图 -->
        <div v-else class="rooms-container">
            <div v-for="(floor, floorIndex) in diagramData?.floorDiagramList || []" :key="floor.floorId" class="floor">
                <div class="floor-label">{{ floor.floorName }}</div>
                <div class="room-grid">
                    <div v-for="room in floor.rooms" :key="room.roomId" :class="['room', getRoomStatusClass(room)]"
                        @click="onRoomClick(room)">
                        <span class="room-number">{{ room.roomName }}</span>

                        <!-- 特殊状态标识 -->
                        <span v-if="hasSpecialStatus(room)" :class="['room-status-half']">
                            <span :class="['room-half', getSpecialStatusClass(room)]"></span>
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 更多筛选弹框 -->
        <van-popup v-model:show="showFilterPopup" position="bottom" :style="{ height: '70%' }" round
            class="filter-popup">
            <div class="filter-content">
                <div class="filter-header">
                    <h3 class="filter-title">更多筛选</h3>
                    <van-icon name="cross" @click="closeFilterPopup" class="close-icon" />
                </div>

                <div class="filter-form">
                    <!-- 查看日期 -->
                    <van-cell-group>
                        <van-field readonly clickable label="查看日期" v-model="filterForm.viewDate" placeholder="请选择查看日期"
                            @click="showViewDatePicker = true" />
                    </van-cell-group>

                    <!-- 用途（物业类型） -->
                    <van-cell-group v-if="propertyTypeOptions.length > 0">
                        <van-field label="用途">
                            <template #input>
                                <div class="property-type-checkboxes">
                                    <van-checkbox-group v-model="filterForm.propertyTypes" direction="horizontal">
                                        <van-checkbox v-for="type in propertyTypeOptions" :key="type.value"
                                            :name="type.value" shape="square">
                                            {{ type.text }}
                                        </van-checkbox>
                                    </van-checkbox-group>
                                </div>
                            </template>
                        </van-field>
                    </van-cell-group>

                    <!-- 房态 -->
                    <van-cell-group>
                        <van-field label="房态">
                            <template #input>
                                <div class="room-status-checkboxes">
                                    <van-checkbox-group v-model="filterForm.roomStatuses" direction="horizontal">
                                        <van-checkbox v-for="status in roomStatusOptions" :key="status.value"
                                            :name="status.value" shape="square">
                                            {{ status.text }}
                                        </van-checkbox>
                                    </van-checkbox-group>
                                </div>
                            </template>
                        </van-field>
                    </van-cell-group>

                    <!-- 其他筛选条件 -->
                    <van-cell-group>
                        <van-field label="特殊标识">
                            <template #input>
                                <div class="special-status-checkboxes">
                                    <van-checkbox-group v-model="filterForm.specialStatuses" direction="horizontal">
                                        <van-checkbox name="dueSoon" shape="square">即将到期</van-checkbox>
                                        <van-checkbox name="isSelfUse" shape="square">自用</van-checkbox>
                                        <van-checkbox name="isLock" shape="square">锁房</van-checkbox>
                                        <van-checkbox name="isDirty" shape="square">脏房</van-checkbox>
                                        <van-checkbox name="isMaintain" shape="square">维修</van-checkbox>
                                    </van-checkbox-group>
                                </div>
                            </template>
                        </van-field>
                    </van-cell-group>
                </div>

                <!-- 查看日期选择器 -->
                <van-popup v-model:show="showViewDatePicker" position="bottom">
                    <van-date-picker :model-value="viewDate" title="选择查看日期" :min-date="minDate" :max-date="maxDate"
                        @confirm="onViewDateConfirm" @cancel="onViewDateCancel" />
                </van-popup>

                <div class="filter-actions">
                    <van-button class="reset-btn" @click="resetFilter">重置</van-button>
                    <van-button type="primary" class="confirm-btn" @click="applyFilter">确定</van-button>
                </div>
            </div>
        </van-popup>

        <!-- 房间详情弹框 -->
        <van-popup v-model:show="showRoomDetail" position="bottom" :style="{ height: '60%' }" round
            class="room-detail-popup">
            <div v-if="selectedRoom" class="room-detail-content">
                <div class="room-detail-header">
                    <h3 class="room-detail-title">{{ selectedRoom.roomName }}</h3>
                    <van-icon name="cross" @click="closeRoomDetail" class="close-icon" />
                </div>

                <div class="room-detail-body">
                    <div class="room-basic-info">
                        <div class="info-row">
                            <span class="info-label">房间状态：</span>
                            <span class="info-value">{{ selectedRoom.roomStatusName }}</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">物业类型：</span>
                            <span class="info-value">{{ selectedRoom.propertyTypeName }}</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">计租面积：</span>
                            <span class="info-value">{{ selectedRoom.rentArea }}㎡</span>
                        </div>
                        <div class="info-row" v-if="selectedRoom.tablePrice">
                            <span class="info-label">表价：</span>
                            <span class="info-value">¥{{ selectedRoom.tablePrice }}/月</span>
                        </div>
                        <div class="info-row" v-if="selectedRoom.emptyDays > 0">
                            <span class="info-label">空置天数：</span>
                            <span class="info-value">{{ selectedRoom.emptyDays }}天</span>
                        </div>
                    </div>

                    <!-- 合同信息 -->
                    <div v-if="selectedRoom.contractVo" class="contract-info">
                        <h4 class="section-title">合同信息</h4>
                        <div class="info-row">
                            <span class="info-label">合同编号：</span>
                            <span class="info-value">{{ selectedRoom.contractVo.contractNo }}</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">承租人：</span>
                            <span class="info-value">{{ selectedRoom.contractVo.tenantName }}</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">合同租金：</span>
                            <span class="info-value">¥{{ selectedRoom.contractVo.rentPrice }}/月</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">合同期间：</span>
                            <span class="info-value">{{ formatDateRange(selectedRoom.contractVo.startDate,
                                selectedRoom.contractVo.endDate) }}</span>
                        </div>
                    </div>

                    <!-- 订单信息 -->
                    <div v-if="selectedRoom.bookingVo" class="booking-info">
                        <h4 class="section-title">订单信息</h4>
                        <div class="info-row">
                            <span class="info-label">客户名称：</span>
                            <span class="info-value">{{ selectedRoom.bookingVo.customerName }}</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">公司名称：</span>
                            <span class="info-value">{{ selectedRoom.bookingVo.companyName }}</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">预定金额：</span>
                            <span class="info-value">¥{{ selectedRoom.bookingVo.bookingAmount }}</span>
                        </div>
                    </div>

                    <!-- 房源标识 -->
                    <div v-if="selectedRoom.tags && selectedRoom.tags.length > 0" class="room-tags">
                        <h4 class="section-title">房源标识</h4>
                        <div class="tags-container">
                            <van-tag v-for="tag in selectedRoom.tags" :key="tag" type="primary" size="medium">
                                {{ tag }}
                            </van-tag>
                        </div>
                    </div>
                </div>
            </div>
        </van-popup>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, watch } from 'vue';
import { useRoute } from 'vue-router';
import { showToast, showLoadingToast, closeToast } from 'vant';
import {
    getRoomDiagram,
    getRoomTree,
    type RoomDiagramQueryDTO,
    type DiagramVo,
    type RoomDiagramVo,
    type FloorDiagramVo,
    type RoomTreeQueryDTO,
    type RoomOption
} from '../api/room';
import { getUserProjects } from '../api/home';

const route = useRoute();

// 响应式数据
const loading = ref(false);
const diagramData = ref<DiagramVo | null>(null);
const statsData = computed(() => diagramData.value);
const roomTreeData = ref<RoomOption[]>([]);

// 筛选条件
const selectedParcel = ref('');
const selectedBuilding = ref('');
const selectedFloor = ref('');

// 下拉选项
const parcelOptions = ref<Array<{ text: string, value: string }>>([]);
const buildingOptions = ref<Array<{ text: string, value: string }>>([]);
const floorOptions = ref<Array<{ text: string, value: string }>>([]);
const propertyTypeOptions = ref<Array<{ text: string, value: string }>>([]);

// 更多筛选相关
const showFilterPopup = ref(false);
const filterForm = reactive({
    viewDate: '',        // 查看日期
    propertyTypes: [],   // 用途（物业类型）
    roomStatuses: [],    // 房态
    specialStatuses: []  // 特殊状态
});

// 日期选择器相关
const showViewDatePicker = ref(false);
const viewDate = ref(['2024', '01', '01']); // 日期选择器需要字符串数组格式
const minDate = new Date(2020, 0, 1);
const maxDate = new Date(2030, 11, 31);

// 房间详情
const showRoomDetail = ref(false);
const selectedRoom = ref<RoomDiagramVo | null>(null);

// 当前项目ID（从路由参数或用户默认项目获取）
const currentProjectId = ref(route.query.projectId as string || '');

// 房态图例配置
const legendItems = [
                { text: '空置', class: 'vacant' },
                { text: '在租', class: 'rented' },
                { text: '待生效/签约中/已预定', class: 'pending' },
                { text: '不可招商', class: 'unavailable' },
                { text: '自用', class: 'self-use', isHalf: true },
                { text: '未进场', class: 'not-entered', isHalf: true },
                { text: '未出场', class: 'not-exited', isHalf: true },
];

// 筛选选项
const roomStatusOptions = [
    { text: '空置', value: 1 },
    { text: '在租', value: 2 },
    { text: '待生效/签约中/已预定', value: 3 },
    { text: '不可招商', value: 4 }
];

// 生命周期
onMounted(async () => {
    await initPage();
});

// 监听筛选条件变化
watch([selectedParcel, selectedBuilding, selectedFloor], () => {
    loadRoomDiagram();
});

// 方法定义
const initPage = async () => {
    try {
        loading.value = true;

        // 如果没有项目ID，获取用户默认项目
        // if (!currentProjectId.value) {
        //     const projects = await getUserProjects();
        //     if (projects.data && projects.data.length > 0) {
        //         currentProjectId.value = projects.data[0].id;
        //     }
        // }
        let currentProject = localStorage.getItem('currentProject')
        if (currentProject) {
            currentProject = JSON.parse(currentProject)
        } else {

        }
        // @ts-ignore
        currentProjectId.value = currentProject?.id
        if (currentProjectId.value) {
            // 并行加载房间树和房态图数据
            await Promise.all([
                loadRoomTree(),
                loadRoomDiagram()
            ]);
        }
    } catch (error) {
        console.error('初始化页面失败:', error);
        showToast('加载数据失败');
    } finally {
        loading.value = false;
    }
};

// 加载房间树数据
const loadRoomTree = async () => {
    try {
        const params: RoomTreeQueryDTO = {
            projectId: currentProjectId.value,
            pageNum: 1,
            pageSize: 1000
        };

        const response = await getRoomTree(params);
        roomTreeData.value = response.data || [];

        // 解析树形数据，构建筛选选项
        buildFilterOptions();
    } catch (error) {
        console.error('加载房间树失败:', error);
    }
};

// 构建筛选选项
const buildFilterOptions = () => {
    const parcels = new Set<string>();
    const buildings = new Set<string>();
    const floors = new Set<string>();
    const propertyTypes = new Set<string>();

    const traverseTree = (nodes: RoomOption[]) => {
        nodes.forEach(node => {
            if (node.level === 2 && node.name) { // 地块
                parcels.add(node.name);
            } else if (node.level === 3 && node.name) { // 楼栋
                buildings.add(node.name);
            } else if (node.level === 4) { // 房源
                if (node.floorName) floors.add(node.floorName);
                if (node.propertyType) propertyTypes.add(node.propertyType);
            }

            if (node.children) {
                traverseTree(node.children);
            }
        });
    };

    traverseTree(roomTreeData.value);

    // 构建选项数组
    parcelOptions.value = [
        { text: '全部地块', value: '' },
        ...Array.from(parcels).map(name => ({ text: name, value: name }))
    ];

    buildingOptions.value = [
        { text: '全部楼栋', value: '' },
        ...Array.from(buildings).map(name => ({ text: name, value: name }))
    ];

    floorOptions.value = [
        { text: '全部楼层', value: '' },
        ...Array.from(floors).map(name => ({ text: name, value: name }))
    ];

    propertyTypeOptions.value = Array.from(propertyTypes).map(type => ({
        text: type, // TODO: 可以映射为中文名称
        value: type
    }));
};

// 加载房态图数据
const loadRoomDiagram = async () => {
    try {
        const params: RoomDiagramQueryDTO = {
            projectId: currentProjectId.value
        };

        // 添加筛选条件
        if (selectedParcel.value) {
            const parcelId = getParcelIdByName(selectedParcel.value);
            if (parcelId) {
                params.parcelId = parcelId;
            }
        }
        if (selectedBuilding.value) {
            const buildingId = getBuildingIdByName(selectedBuilding.value);
            if (buildingId) {
                params.buildingId = buildingId;
            }
        }
        if (selectedFloor.value) {
            const floorId = getFloorIdByName(selectedFloor.value);
            if (floorId) {
                params.floorId = floorId;
            }
        }

        // 添加更多筛选条件
        if (filterForm.viewDate) {
            params.diagramDate = filterForm.viewDate;
        }
        if (filterForm.propertyTypes.length > 0) {
            // 目前API似乎只支持单个物业类型
            params.propertyType = filterForm.propertyTypes[0] as string;
        }
        if (filterForm.roomStatuses.length > 0) {
            // 目前API似乎只支持单个房间状态
            params.roomStatus = filterForm.roomStatuses[0] as number;
        }

        // 特殊状态筛选
        (filterForm.specialStatuses as string[]).forEach((status: string) => {
            switch (status) {
                case 'dueSoon':
                    params.dueSoon = true;
                    break;
                case 'isSelfUse':
                    params.isSelfUse = true;
                    break;
                case 'isLock':
                    params.isLock = true;
                    break;
                case 'isDirty':
                    params.isDirty = true;
                    break;
                case 'isMaintain':
                    params.isMaintain = true;
                    break;
            }
        });

        const response = await getRoomDiagram(params);
        diagramData.value = response.data;

        // 更新物业类型选项（基于实际返回的数据）
        if (response.data?.propertyList) {
            propertyTypeOptions.value = response.data.propertyList.map(type => ({
                text: type,
                value: type
            }));
        }
    } catch (error) {
        console.error('加载房态图失败:', error);
        showToast('加载房态图失败');
    }
};

// 名称到ID的映射方法
const getParcelIdByName = (parcelName: string): string | null => {
    const findParcel = (nodes: RoomOption[]): string | null => {
        for (const node of nodes) {
            if (node.level === 2 && node.name === parcelName) {
                return node.parcelId;
            }
            if (node.children) {
                const result = findParcel(node.children);
                if (result) return result;
            }
        }
        return null;
    };
    return findParcel(roomTreeData.value);
};

const getBuildingIdByName = (buildingName: string): string | null => {
    const findBuilding = (nodes: RoomOption[]): string | null => {
        for (const node of nodes) {
            if (node.level === 3 && node.name === buildingName) {
                return node.buildingId;
            }
            if (node.children) {
                const result = findBuilding(node.children);
                if (result) return result;
            }
        }
        return null;
    };
    return findBuilding(roomTreeData.value);
};

const getFloorIdByName = (floorName: string): string | null => {
    const findFloor = (nodes: RoomOption[]): string | null => {
        for (const node of nodes) {
            if (node.level === 4 && node.floorName === floorName) {
                return node.floorId;
            }
            if (node.children) {
                const result = findFloor(node.children);
                if (result) return result;
            }
        }
        return null;
    };
    return findFloor(roomTreeData.value);
};

// 筛选条件变化处理
const onParcelChange = () => {
    // 地块变化时重置楼栋和楼层
    selectedBuilding.value = '';
    selectedFloor.value = '';
    updateBuildingOptions();
};

const onBuildingChange = () => {
    // 楼栋变化时重置楼层
    selectedFloor.value = '';
    updateFloorOptions();
};

const onFloorChange = () => {
    // 楼层变化会自动触发watch重新加载数据
};

// 更新楼栋选项（基于选择的地块）
const updateBuildingOptions = () => {
    console.log('updateBuildingOptions', selectedParcel.value)
    if (!selectedParcel.value) {
        // 如果没有选择地块，显示所有楼栋
        buildFilterOptions();
        return;
    }

    const buildings = new Set<string>();
    const findBuildingsInParcel = (nodes: RoomOption[]) => {
        nodes.forEach(node => {
            if (node.level === 2 && node.name === selectedParcel.value) {
                // 找到对应地块，遍历其子节点（楼栋）
                if (node.children) {
                    node.children.forEach(building => {
                        if (building.level === 3 && building.name) {
                            buildings.add(building.name);
                        }
                    });
                }
            } else if (node.children) {
                findBuildingsInParcel(node.children);
            }
        });
    };

    findBuildingsInParcel(roomTreeData.value);
    
    buildingOptions.value = [
        { text: '全部楼栋', value: '' },
        ...Array.from(buildings).map(name => ({ text: name, value: name }))
    ];
};

// 更新楼层选项（基于选择的楼栋）
const updateFloorOptions = () => {
    if (!selectedBuilding.value) {
        // 如果没有选择楼栋，显示所有楼层
        buildFilterOptions();
        return;
    }

    const floors = new Set<string>();
    const findFloorsInBuilding = (nodes: RoomOption[]) => {
        nodes.forEach(node => {
            if (node.level === 3 && node.name === selectedBuilding.value) {
                // 找到对应楼栋，遍历其子节点（房源）获取楼层信息
                if (node.children) {
                    node.children.forEach(room => {
                        if (room.level === 4 && room.floorName) {
                            floors.add(room.floorName);
                        }
                    });
                }
            } else if (node.children) {
                findFloorsInBuilding(node.children);
            }
        });
    };

    findFloorsInBuilding(roomTreeData.value);
    
    floorOptions.value = [
        { text: '全部楼层', value: '' },
        ...Array.from(floors).map(name => ({ text: name, value: name }))
    ];
};

// 获取房间状态样式类
const getRoomStatusClass = (room: RoomDiagramVo): string => {
    switch (room.roomStatus) {
        case 1: return 'vacant';      // 空置
        case 2: return 'rented';      // 在租
        case 3: return 'pending';     // 待生效/签约中/已预定
        case 4: return 'unavailable'; // 不可招商
        default: return 'vacant';
    }
};

// 检查是否有特殊状态
const hasSpecialStatus = (room: RoomDiagramVo): boolean => {
    return room.isSelfUse || room.needCheckIn || room.needCheckOut;
};

// 获取特殊状态样式类
const getSpecialStatusClass = (room: RoomDiagramVo): string => {
    if (room.isSelfUse) return 'self-use';
    if (room.needCheckIn) return 'not-entered';
    if (room.needCheckOut) return 'not-exited';
    return '';
};

// 房间点击事件
const onRoomClick = (room: RoomDiagramVo) => {
    selectedRoom.value = room;
    showRoomDetail.value = true;
};

// 关闭房间详情
const closeRoomDetail = () => {
    showRoomDetail.value = false;
    selectedRoom.value = null;
};

        // 显示更多筛选
const showMoreFilter = () => {
    showFilterPopup.value = true;
};

        // 关闭筛选弹框
const closeFilterPopup = () => {
    showFilterPopup.value = false;
};

        // 查看日期确认
const onViewDateConfirm = (value: string[]) => {
    filterForm.viewDate = `${value[0]}-${value[1]}-${value[2]}`;
    showViewDatePicker.value = false;
};

        // 查看日期取消
const onViewDateCancel = () => {
    showViewDatePicker.value = false;
};

        // 格式化日期
const formatDate = (date: Date): string => {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            return `${year}-${month}-${day}`;
};

// 格式化日期范围
const formatDateRange = (startDate: string | null, endDate: string | null): string => {
    if (!startDate || !endDate) return '-';
    return `${startDate} 至 ${endDate}`;
};

        // 应用筛选
const applyFilter = () => {
    loadRoomDiagram();
    showFilterPopup.value = false;
};

        // 重置筛选
const resetFilter = () => {
    filterForm.viewDate = '';
    filterForm.propertyTypes = [];
    filterForm.roomStatuses = [];
    filterForm.specialStatuses = [];
    const today = new Date();
    viewDate.value = [
        today.getFullYear().toString(),
        (today.getMonth() + 1).toString().padStart(2, '0'),
        today.getDate().toString().padStart(2, '0')
    ];
    
    // 重新加载数据
    loadRoomDiagram();
};
</script>

<style scoped lang="less">
.page {
    background-color: #f7f8fa;
    min-height: 100vh;
}

.nav-bar {
    background-color: #fff;

    :deep(.van-nav-bar__title) {
        font-size: 36px;
        font-weight: 500;
        color: #323233;
    }
}

// 统计信息栏
.stats-bar {
    display: flex;
    justify-content: space-around;
    padding: 32px 0;
    background-color: #fff;
    margin-bottom: 16px;

    .stats-item {
        display: flex;
        flex-direction: column;
        align-items: center;

        .stats-count {
            font-size: 48px;
            font-weight: bold;
            color: #1989fa;
            line-height: 1;
        }

        .stats-label {
            font-size: 24px;
            color: #646566;
            margin-top: 8px;
        }
    }
}

.filter-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 32px;
    background-color: #fff;

    :deep(.van-dropdown-menu__bar) {
        box-shadow: none !important;
    }

    .dropdown-menu {
        flex: 1;

        :deep(.van-dropdown-menu__item) {
            color: #1989fa;
            font-size: 28px;
        }
    }

    .more-btn {
        margin-left: 32px;
        padding: 12px 24px;
        font-size: 28px;
        border-radius: 8px;
    }
}

.legend {
    display: flex;
    flex-wrap: wrap;
    padding: 24px 32px;
    background-color: #fff;
    margin-bottom: 16px;
    gap: 24px;

    .legend-item {
        display: flex;
        align-items: center;

        .legend-color {
            width: 24px;
            height: 24px;
            border-radius: 4px;
            margin-right: 12px;

            &.vacant {
                background-color: #52b93c;
            }

            &.rented {
                background-color: #ea5e5e;
            }

            &.pending {
                background-color: #f89538;
            }

            &.unavailable {
                background-color: #8b929c;
            }

            &.self-use {
                background-color: #3583ff;
            }

            &.not-entered {
                background-color: #60d1ff;
            }

            &.not-exited {
                background-color: #d73eff;
            }
        }

        .legend-color-half {
            width: 24px;
            height: 24px;
            background: #fff;
            position: relative;
            overflow: hidden;
            border-radius: 2px;
            margin-right: 12px;

            .half {
                color: white;
                width: 24px;
                height: 24px;
                display: flex;
                align-items: flex-end;
                justify-content: center;
                line-height: 1.1;
                text-align: center;
                position: absolute;
                top: -12px;
                right: -12px;
                transform: rotate(45deg);

                &.self-use {
                    background-color: #3583ff;
                }

                &.not-entered {
                    background-color: #60d1ff;
                }

                &.not-exited {
                    background-color: #d73eff;
                }
            }
        }

        .legend-text {
            font-size: 24px;
            color: #323233;
        }
    }
}

.loading-center {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 400px;
}

.rooms-container {
    padding: 0 32px;
}

.floor {
    display: flex;
    align-items: flex-start;
    margin-bottom: 48px;

    .floor-label {
        font-size: 28px;
        font-weight: 500;
        color: #323233;
        padding-right: 32px;
        min-width: 100px;
        text-align: right;
    }

    .room-grid {
        width: 0;
        flex: 1;
        display: grid;
        grid-template-columns: repeat(6, 1fr);
        gap: 16px;

        .room {
            aspect-ratio: 1;
            display: flex;
            justify-content: center;
            align-items: center;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            position: relative;

            .room-number {
                font-size: 28px;
                font-weight: 500;
                color: #fff;
            }

            .room-status-half {
                width: 32px;
                height: 32px;
                background: transparent;
                position: absolute;
                top: 0;
                right: 0;
                overflow: hidden;
                border-radius: 8px;

                .room-half {
                    background-color: #3583ff;
                    color: white;
                    width: 32px;
                    height: 32px;
                    display: flex;
                    align-items: flex-end;
                    justify-content: center;
                    line-height: 1.1;
                    text-align: center;
                    position: absolute;
                    top: -16px;
                    right: -16px;
                    transform: rotate(45deg);
                    border: 2px solid #f7f8fa;

                    &.self-use {
                        background-color: #3583ff;
                    }

                    &.not-entered {
                        background-color: #60d1ff;
                    }

                    &.not-exited {
                        background-color: #d73eff;
                    }
                }
            }

            &.vacant {
                background-color: #52b93c;
            }

            &.rented {
                background-color: #ea5e5e;
            }

            &.pending {
                background-color: #f89538;
            }

            &.unavailable {
                background-color: #8b929c;
            }

            &.self-use {
                background-color: #3583ff;
            }

            &.not-entered {
                background-color: #60d1ff;
            }

            &.not-exited {
                background-color: #d73eff;
            }

            &:hover {
                transform: scale(1.05);
                box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
            }

            &:active {
                transform: scale(0.98);
            }
        }
    }
}

/* 筛选弹框样式 */
.filter-popup {
    .filter-content {
        padding: 0;
        background-color: #fff;
    }

    .filter-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 60px 80px;
        border-bottom: 1px solid #f0f0f0;

        .filter-title {
            font-size: 72px;
            font-weight: bold;
            color: #333;
            margin: 0;
        }

        .close-icon {
            font-size: 80px;
            color: #999;
            cursor: pointer;
        }
    }

    .filter-form {
        padding: 0 40px;
        max-height: 50vh;
        overflow-y: auto;

        :deep(.van-cell-group) {
            margin-bottom: 40px;
        }

        :deep(.van-field__label) {
            font-size: 60px;
            color: #333;
            width: 280px;
        }

        :deep(.van-field__value) {
            font-size: 60px;
        }

        :deep(.van-field__control) {
            font-size: 60px;
        }

        // 复选框样式
        .property-type-checkboxes,
        .room-status-checkboxes,
        .special-status-checkboxes {
            width: 100%;
            
            :deep(.van-checkbox-group) {
                display: flex;
                flex-wrap: wrap;
                gap: 24px;
            }
            
            :deep(.van-checkbox) {
                margin-right: 0;
                margin-bottom: 24px;
                
                .van-checkbox__label {
                    font-size: 48px;
                    color: #333;
                    margin-left: 16px;
                }
                
                .van-checkbox__icon {
                    font-size: 40px;
                }
            }
        }
    }

    .filter-actions {
        display: flex;
        gap: 40px;
        padding: 60px 80px;
        border-top: 1px solid #f0f0f0;
        background-color: #fff;

        .reset-btn,
        .confirm-btn {
            flex: 1;
            height: 160px;
            font-size: 64px;
            border-radius: 16px;
        }

        .reset-btn {
            background-color: #f8f9fa;
            border-color: #e9ecef;
            color: #6c757d;
        }

        .confirm-btn {
            background-color: #1890ff;
            border-color: #1890ff;
        }
    }
}

/* 房间详情弹框样式 */
.room-detail-popup {
    .room-detail-content {
        padding: 0;
        background-color: #fff;
    }

    .room-detail-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 60px 80px;
        border-bottom: 1px solid #f0f0f0;

        .room-detail-title {
            font-size: 72px;
            font-weight: bold;
            color: #333;
            margin: 0;
        }

        .close-icon {
            font-size: 80px;
            color: #999;
            cursor: pointer;
        }
    }

    .room-detail-body {
        padding: 40px 80px;
        max-height: 50vh;
        overflow-y: auto;

        .section-title {
            font-size: 56px;
            font-weight: bold;
            color: #333;
            margin: 40px 0 20px 0;

            &:first-child {
                margin-top: 0;
            }
        }

        .info-row {
            display: flex;
            padding: 16px 0;
            border-bottom: 1px solid #f5f5f5;

            .info-label {
                font-size: 48px;
                color: #666;
                min-width: 200px;
            }

            .info-value {
                font-size: 48px;
                color: #333;
                flex: 1;
            }
        }

        .tags-container {
            display: flex;
            flex-wrap: wrap;
            gap: 16px;
            margin-top: 20px;

            :deep(.van-tag) {
                font-size: 32px;
                padding: 8px 16px;
            }
        }
    }
}
</style>